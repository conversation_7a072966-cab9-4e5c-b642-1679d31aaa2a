import 'package:fusion/extension/double.dart';
import 'package:fusion/pages/order/widget/accessory_data_interface.dart';
import 'package:fusion/pages/order/widget/order_item_data.dart';
import 'package:fusion/store/user_store.dart';
import 'package:fusion/utils/payment_method.dart';
import 'package:fusion/widgets/accessory_tag.dart';
import 'package:openapi/openapi.dart';
import 'order_detail_data_interface.dart';
import 'order_sticker.dart';
import 'package:fusion/gen/colors.gen.dart';
import 'package:flutter/painting.dart';
import 'package:fusion/pages/order/widget/color_on_order_state.dart';

class OrderDetailData implements OrderDetailDataInterface {
  final V1OrderOnly base;
  final V1orderGoodsInfo info;
  final List<OrderSticker>? stickers;
  final BffInterfaceBuyerSteamInfo200ResponseDataResultInner? buyerGuard;

  OrderDetailData({
    required this.base,
    required this.info,
    this.stickers,
    this.buyerGuard,
  });

  OrderDetailData copyWith({
    V1OrderOnly? base,
    V1orderGoodsInfo? info,
    List<OrderSticker>? stickers,
    BffInterfaceBuyerSteamInfo200ResponseDataResultInner? buyerGuard,
  }) {
    return OrderDetailData(
      base: base ?? this.base,
      info: info ?? this.info,
      stickers: stickers ?? this.stickers,
      buyerGuard: buyerGuard ?? this.buyerGuard,
    );
  }

  @override
  OrderState get state => OrderState.fromInt(base.state);

  @override
  TradeInitiator get tradeInitiator =>
      TradeInitiator.fromString(base.quoteInitiator ?? '');

  Color get stateMessageColor {
    switch (state) {
      case OrderState.waitingCreateTradeOffer:
        if ((tradeInitiator == TradeInitiator.seller && isBuyer) ||
            (tradeInitiator == TradeInitiator.buyer && !isBuyer)) {
          return AppColors.blue206CFE;
        }
        return OrderState.fromInt(base.state).textColor;
      case OrderState.waitingConfirmTradeOffer:
        if (isBuyer) {
          return AppColors.blue206CFE;
        }
        return OrderState.fromInt(base.state).textColor;
      case OrderState.waitingResponseTradeOffer:
      case OrderState.waitingReceiveTradeOffer:
        if ((tradeInitiator == TradeInitiator.buyer && isBuyer) ||
            (tradeInitiator == TradeInitiator.seller && !isBuyer)) {
          return AppColors.blue206CFE;
        }
        return OrderState.fromInt(base.state).textColor;
      default:
        return OrderState.fromInt(base.state).textColor;
    }
  }

  @override
  int get orderStatusCountdown => base.countdown ?? 0;

  @override
  String get stateMessage => base.stateMsg ?? "未知状态";

  @override
  String? get stateReason =>
      state == OrderState.failure || state == OrderState.tradeOfferPending
          ? base.subhead
          : null;

  @override
  bool get isBuyer => UserStore.to.uId == base.buyerUid;

  @override
  String get partnerName => base.otherUsername ?? "";

  @override
  String get id => base.orderId ?? "";

  @override
  String get createTime => base.purchaseTime ?? "";

  @override
  String get price =>"¥${(isBuyer==true ? base.unitPrice : base.sellerReceiveAmount)?.removeTrailingZero()}";

  @override
  PaymentMethod get paymentMethod => PaymentMethod.fromInt(base.payType);

  @override
  List<AccessoryCardData> get accessories => [
        AccessoryCardData(
          coverUrl: info.item?.goodsInfo?.iconUrl ?? "",
          tags: getTags(),
          name: info.item?.goodsInfo?.marketName ?? "",
          price: '${base.unitPrice?.removeTrailingZero()}',
          wear: base.wear,
          stickers: stickers,
        )
      ];

  List<AccessoryTagData>? getTags() {
    final tags_ = info.item?.goodsInfo?.tags;
    if (tags_ == null) {
      return null;
    }
    final tags = <AccessoryTagData>[];
    if (tags_.rarity?.isShow ?? false) {
      tags.add(
        AccessoryTagData(
          backgroundColor: tags_.rarity?.color,
          name: tags_.rarity?.shortTitle,
        ),
      );
    }
    if (tags_.exterior?.isShow ?? false) {
      tags.add(
        AccessoryTagData(
          backgroundColor: tags_.exterior?.color,
          name: tags_.exterior?.shortTitle,
        ),
      );
    }
    if (tags_.quality?.isShow ?? false) {
      tags.add(
        AccessoryTagData(
          backgroundColor: tags_.quality?.color,
          name: tags_.quality?.shortTitle,
        ),
      );
    }
    return tags.isEmpty ? null : tags;
  }

  @override
  OrderType get type {
    /// 求购订单
    if (base.tradeType == 2) {
      if (isBuyer) {
        return OrderType.want;
      }
      return OrderType.supply;
    }

    if (isBuyer) {
      return OrderType.buy;
    }
    return OrderType.sell;
  }

  @override
  List<OrderAction>? get actions {
    switch (state) {
      case OrderState.none:
      case OrderState.waitingPay:
        return null;
      case OrderState.waitingCreateTradeOffer:
        if ((base.quoteInitiator == "buyer" && isBuyer)) {
          return [OrderAction.createTradeOffer];
        } else if (base.quoteInitiator == "seller" && !isBuyer) {
          return [OrderAction.delivery];
        }
        return null;
      case OrderState.waitingResponseTradeOffer:
        if ((base.quoteInitiator == "buyer" && !isBuyer)) {
          return [OrderAction.delivery];
        } else if (base.quoteInitiator == "seller" && isBuyer) {
          return [OrderAction.acceptTradeOffer];
        }
        return null;
      case OrderState.waitingReceiveTradeOffer:
        if (isBuyer) {
          return [OrderAction.acceptTradeOffer];
        }
        return null;
      case OrderState.waitingConfirmTradeOffer:
      case OrderState.exception:
      case OrderState.tradeOfferPending:
      case OrderState.success:
      case OrderState.failure:
      case OrderState.tradeProtection:
        return null;
    }
  }

  @override
  bool get enableCancel => base.isCancel == true;

  @override
  bool get enableFeedback => base.isFeedback == true;
}
