import 'package:flutter/painting.dart';
import 'package:fusion/pages/order/widget/color_on_order_state.dart';
import 'package:fusion/utils/payment_method.dart';
import 'package:fusion/widgets/accessory_tag.dart';
import '../model/order_sticker.dart';

/// enum OrderBigStatus {
//   // 未知
//   OrderBigStatus_UNKNOWN = 0;
//   // 待支付
//   OrderBigStatus_WAIT_PAY = 1;
//   // 待发起交易报价
//   OrderBigStatus_WAIT_CREATE_TRADE = 2;
//   // 待回应交易报价
//   OrderBigStatus_WAIT_RESPOND_TRADE = 3;
//   // 待确认交易报价
//   OrderBigStatus_WAIT_CONFIRM_TRADE = 4;
//   // 待接收报价
//   OrderBigStatus_WAIT_RECEIVE = 5;
//   // 交易报价暂挂
//   OrderBigStatus_TRADE_SUSPEND = 6;
//   // 交易异常
//   OrderBigStatus_TRADE_ABNORMAL = 7;
//   // 交易成功
//   OrderBigStatus_TRADE_SUCCESS = 8;
//   // 交易失败
//   OrderBigStatus_TRADE_FAIL = 9;
//   // 交易保护中
//   OrderBigStatus_ = 10;
// }
/// 顺序和后台定义的状态码需要严格一致
enum OrderState {
  none, // 0
  waitingPay, // 1
  waitingCreateTradeOffer, // 2
  waitingResponseTradeOffer, // 3
  waitingConfirmTradeOffer, // 4
  waitingReceiveTradeOffer, // 5
  tradeOfferPending, // 6
  exception, // 7
  success, // 8
  failure, // 9
  
  ;

  /// 根据数字返回枚举
  static OrderState fromInt(int? value) {
    if (value == null || value >= OrderState.values.length || value < 0) {
      return OrderState.none;
    }
    return OrderState.values[value];
  }

  /// 已经不可再有变化的状态
  static List<OrderState> frozen = [
    OrderState.exception,
    OrderState.success,
    OrderState.failure,
  ];

  /// 根据状态返回该状态的可能动作
  OrderAction? get actionValue {
    switch (this) {
      case OrderState.none:
        return null;
      case OrderState.waitingPay:
        return OrderAction.pay;
      case OrderState.waitingCreateTradeOffer:
        return OrderAction.createTradeOffer;
      case OrderState.waitingConfirmTradeOffer:

        /// 只提醒，无法操作
        return null;
      case OrderState.waitingReceiveTradeOffer:
      case OrderState.waitingResponseTradeOffer:
        return OrderAction.acceptTradeOffer;
      case OrderState.tradeOfferPending:

        /// 只提醒，无法操作
        return null;
      case OrderState.exception:

        /// 只提醒，无法操作
        return null;
      case OrderState.success:

        /// 只提醒，无法操作
        return null;
      case OrderState.failure:

        /// 只提醒，无法操作
        return null;
    }
  }

  /// 该状态是否显示倒计时
  bool get enableCountdown => !OrderState.frozen.contains(this);

  String get filterOptionText {
    switch (this) {
      case OrderState.none:
        return "全部状态";
      case OrderState.waitingPay:
        return "待付款";
      case OrderState.waitingCreateTradeOffer:
        return "等待发起报价";
      case OrderState.waitingConfirmTradeOffer:
        return "等待确认报价";
      case OrderState.waitingReceiveTradeOffer:
        return "等待接受报价";
      case OrderState.waitingResponseTradeOffer:
        return "等待回应报价";
      case OrderState.tradeOfferPending:
        return "交易暂挂";
      case OrderState.failure:
      case OrderState.exception:
        return "交易失败";
      case OrderState.success:
        return "交易成功";
    }
  }
}

enum OrderAction {
  /// 支付
  pay,

  /// 发起报价
  createTradeOffer,

  /// 接受报价
  acceptTradeOffer,

  /// 发货
  delivery,

  /// 取消求购
  cancelWant,

  /// 暂停求购
  pauseWant,

  /// 开启求购
  resumeWant,

  /// 改价
  changeSellingPrice,
  ;

  String get actionText => switch (this) {
        OrderAction.createTradeOffer => "发起报价",
        OrderAction.delivery => "前往发货",
        OrderAction.cancelWant => "取消求购",
        OrderAction.pauseWant => "暂停求购",
        OrderAction.resumeWant => "开启求购",
        OrderAction.acceptTradeOffer => "接受报价",
        OrderAction.pay => "立即支付",
        OrderAction.changeSellingPrice => "改价",
      };

  static OrderAction? fromOrderStatus(int? state) {
    final s = OrderState.fromInt(state);
    return s.actionValue;
  }
}

enum OrderType {
  none,

  /// 购买订单
  buy,

  /// 出售订单
  sell,

  /// 求购订单
  want,

  /// 供应订单
  supply,
  ;

  String get name {
    switch (this) {
      case OrderType.none:
        return "未知";
      case OrderType.buy:
        return "购买";
      case OrderType.sell:
        return "出售";
      case OrderType.want:
        return "求购";
      case OrderType.supply:
        return "供应";
    }
  }
}

///交易发起方
enum TradeInitiator {
  /// 未知
  unknown,

  /// 买家
  buyer,

  /// 卖家
  seller;

  /// 根据数字返回枚举
  static TradeInitiator fromString(String? value) {
    switch (value) {
      case 'buyer':
        return TradeInitiator.buyer;
      case 'seller':
        return TradeInitiator.seller;
      default:
        return TradeInitiator.unknown;
    }
  }
}

abstract class OrderItemData {
  String get id;

  OrderType get type;

  /// 是否显示顶部的时间 + 状态栏
  bool get displayTopBar;

  /// 顶部的日期时间
  String? get topDateTime;

  /// 是否允许暂挂时显示对应icon，点击后会有对应说明
  bool get usePendingIcon => false;

  /// 订单状态
  OrderState get state => OrderState.none;

  /// 订单状态
  String? get stateMessage;

  /// 订单状态颜色
  Color get stateMessageColor => state.textColor;

  TradeInitiator get tradeInitiator=> TradeInitiator.unknown;

  /// 是否和封面在同一行显示状态文字
  bool get useInlineStateMessage;

  /// 订单状态对应的倒计时
  int get orderStatusCountdown;

  bool get orderStatusCountdownEffective => orderStatusCountdown > 0;

  /// 使用内联的倒计时；倒计时会在价格一行
  bool get useInlineCountdown => orderStatusCountdown > 0 && useInlineActions;

  /// 封面图。最多显示4张
  List<String> get coverUrls;

  /// 封面标签
  List<AccessoryTagData>? get coverTags;

  /// 名称
  String get name;

  /// 价格
  double get price;

  /// 价格说明。如求购价后面显示“先最高100”
  String? get priceTip;

  /// 过期价格，通过划线显示
  double? get expiredPrice;

  /// 字符串类型的磨损，用于直接显示
  String? get wear;

  /// 玄学名称
  String? get styleName;

  /// 印花列表
  List<OrderSticker>? get stickers;


  /// 卖家留言
  String? get sellerMessage => null;

  /// 该订单需求饰品数量
  int get requiredCount;

  /// 完成的饰品数量
  int get finishedCount;

  /// 支付方式
  PaymentMethod get paymentMethod;

  /// 是否显示支付方式
  bool get displayPaymentMethod;

  /// 支付方式对应显示的名称。余额 可能显示为 “余额0元求购”
  String? get paymentMethodName => paymentMethod.getName();

  /// 可能的操作
  List<OrderAction>? get actions;

  /// 是否显示动作按钮，历史记录中不显示
  bool get enableActions;

  /// 使用内联的动作按钮。动作按钮会在名称一行
  bool get useInlineActions =>
      enableActions && !(actions != null && actions!.length > 1);
}
